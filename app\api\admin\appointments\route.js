import { NextResponse } from 'next/server';
import { authenticateAdmin } from '../../../../lib/authUtils.js';
import {
  readAppointments,
  updateAppointmentStatus,
  deleteAppointment,
  getAppointmentsByDateRange
} from '../../../../lib/appointmentUtils.js';
import { getDashboardStats, formatAppointmentForDisplay } from '../../../../lib/adminUtils.js';

// GET - Fetch appointments with optional filters
export async function GET(request) {
  try {
    // Check authentication
    const user = authenticateAdmin(request);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Non autorizzato' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    if (action === 'stats') {
      // Return dashboard statistics
      const stats = getDashboardStats();
      return NextResponse.json({
        success: true,
        data: stats
      });
    }

    let appointments;

    if (startDate && endDate) {
      // Get appointments by date range
      appointments = getAppointmentsByDateRange(startDate, endDate);
    } else {
      // Get all appointments
      appointments = readAppointments();
    }

    // Format appointments for display
    const formattedAppointments = appointments.map(formatAppointmentForDisplay);

    // Sort by date and time (most recent first)
    formattedAppointments.sort((a, b) => {
      const dateA = new Date(`${a.dataAppuntamento} ${a.orario}`);
      const dateB = new Date(`${b.dataAppuntamento} ${b.orario}`);
      return dateB - dateA;
    });

    return NextResponse.json({
      success: true,
      data: formattedAppointments,
      total: formattedAppointments.length
    });

  } catch (error) {
    console.error('Error fetching appointments:', error);
    return NextResponse.json(
      { success: false, message: 'Errore del server' },
      { status: 500 }
    );
  }
}

// PUT - Update appointment status
export async function PUT(request) {
  try {
    // Check authentication
    const user = authenticateAdmin(request);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Non autorizzato' },
        { status: 401 }
      );
    }

    const { appointmentId, status } = await request.json();

    if (!appointmentId || !status) {
      return NextResponse.json(
        { success: false, message: 'ID appuntamento e status sono obbligatori' },
        { status: 400 }
      );
    }

    const success = updateAppointmentStatus(appointmentId, status);

    if (!success) {
      return NextResponse.json(
        { success: false, message: 'Appuntamento non trovato' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Status aggiornato con successo'
    });

  } catch (error) {
    console.error('Error updating appointment:', error);
    return NextResponse.json(
      { success: false, message: 'Errore del server' },
      { status: 500 }
    );
  }
}

// DELETE - Delete appointment
export async function DELETE(request) {
  try {
    // Check authentication
    const user = authenticateAdmin(request);
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Non autorizzato' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const appointmentId = searchParams.get('id');

    if (!appointmentId) {
      return NextResponse.json(
        { success: false, message: 'ID appuntamento obbligatorio' },
        { status: 400 }
      );
    }

    const success = deleteAppointment(appointmentId);

    if (!success) {
      return NextResponse.json(
        { success: false, message: 'Appuntamento non trovato' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Appuntamento eliminato con successo'
    });

  } catch (error) {
    console.error('Error deleting appointment:', error);
    return NextResponse.json(
      { success: false, message: 'Errore del server' },
      { status: 500 }
    );
  }
}