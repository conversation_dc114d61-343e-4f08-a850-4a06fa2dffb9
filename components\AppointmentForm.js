'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { services, timeSlots, isWeekday, getMinDate, isValidPhone, isValidEmail } from '../lib/utils';

export default function AppointmentForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm();



  const onSubmit = async (data) => {
    setIsSubmitting(true);
    setSubmitMessage('');

    try {
      const response = await fetch('/api/send-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        setSubmitMessage('Appuntamento confermato! Riceverai una email di conferma.');
        reset();
      } else {
        setSubmitMessage('Errore nell\'invio. Riprova più tardi.');
      }
    } catch (error) {
      setSubmitMessage('Errore nell\'invio. Riprova più tardi.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Nome */}
      <div>
        <label htmlFor="nome" className="form-label">
          Nome *
        </label>
        <input
          type="text"
          id="nome"
          className="form-input"
          {...register('nome', { 
            required: 'Il nome è obbligatorio',
            minLength: { value: 2, message: 'Il nome deve avere almeno 2 caratteri' }
          })}
        />
        {errors.nome && (
          <p className="error-message">{errors.nome.message}</p>
        )}
      </div>

      {/* Cognome */}
      <div>
        <label htmlFor="cognome" className="form-label">
          Cognome *
        </label>
        <input
          type="text"
          id="cognome"
          className="form-input"
          {...register('cognome', { 
            required: 'Il cognome è obbligatorio',
            minLength: { value: 2, message: 'Il cognome deve avere almeno 2 caratteri' }
          })}
        />
        {errors.cognome && (
          <p className="error-message">{errors.cognome.message}</p>
        )}
      </div>

      {/* Numero Telefono */}
      <div>
        <label htmlFor="telefono" className="form-label">
          Numero Telefono *
        </label>
        <input
          type="tel"
          id="telefono"
          className="form-input"
          {...register('telefono', {
            required: 'Il numero di telefono è obbligatorio',
            validate: {
              isValid: (value) =>
                isValidPhone(value) || 'Inserisci un numero di telefono valido'
            }
          })}
        />
        {errors.telefono && (
          <p className="error-message">{errors.telefono.message}</p>
        )}
      </div>

      {/* Email */}
      <div>
        <label htmlFor="email" className="form-label">
          Email *
        </label>
        <input
          type="email"
          id="email"
          className="form-input"
          {...register('email', {
            required: 'L\'email è obbligatoria',
            validate: {
              isValid: (value) =>
                isValidEmail(value) || 'Inserisci un indirizzo email valido'
            }
          })}
        />
        {errors.email && (
          <p className="error-message">{errors.email.message}</p>
        )}
      </div>

      {/* Servizio */}
      <div>
        <label htmlFor="servizio" className="form-label">
          Servizio *
        </label>
        <select
          id="servizio"
          className="form-input"
          {...register('servizio', { required: 'Seleziona un servizio' })}
        >
          <option value="">Seleziona un servizio</option>
          {services.map((service) => (
            <option key={service} value={service}>
              {service}
            </option>
          ))}
        </select>
        {errors.servizio && (
          <p className="error-message">{errors.servizio.message}</p>
        )}
      </div>

      {/* Data Appuntamento */}
      <div>
        <label htmlFor="dataAppuntamento" className="form-label">
          Data Appuntamento *
        </label>
        <input
          type="date"
          id="dataAppuntamento"
          className="form-input"
          min={getMinDate()}
          {...register('dataAppuntamento', { 
            required: 'La data dell\'appuntamento è obbligatoria',
            validate: {
              isWeekday: (value) => 
                isWeekday(value) || 'Seleziona un giorno dal lunedì al venerdì'
            }
          })}
        />
        {errors.dataAppuntamento && (
          <p className="error-message">{errors.dataAppuntamento.message}</p>
        )}
      </div>

      {/* Orario */}
      <div>
        <label htmlFor="orario" className="form-label">
          Orario *
        </label>
        <select
          id="orario"
          className="form-input"
          {...register('orario', { required: 'Seleziona un orario' })}
        >
          <option value="">Seleziona un orario</option>
          <optgroup label="Mattina">
            {timeSlots.slice(0, 5).map((time) => (
              <option key={time} value={time}>
                {time}
              </option>
            ))}
          </optgroup>
          <optgroup label="Pomeriggio">
            {timeSlots.slice(5).map((time) => (
              <option key={time} value={time}>
                {time}
              </option>
            ))}
          </optgroup>
        </select>
        {errors.orario && (
          <p className="error-message">{errors.orario.message}</p>
        )}
      </div>

      {/* Disclaimer */}
      <div className="text-sm text-[var(--secondary-text)] italic bg-gray-50 p-4 rounded-lg">
        *Ti ricordiamo che l'orario potrebbe subire lievi variazioni: se al tuo arrivo un altro cliente è già in fase di servizio, verrai chiamato subito dopo.
      </div>

      {/* Submit Button */}
      <button
        type="submit"
        disabled={isSubmitting}
        className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isSubmitting ? 'Invio in corso...' : 'CONFERMA APPUNTAMENTO'}
      </button>

      {/* Submit Message */}
      {submitMessage && (
        <div className={`text-center p-4 rounded-lg ${
          submitMessage.includes('confermato') 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
        }`}>
          {submitMessage}
        </div>
      )}
    </form>
  );
}
