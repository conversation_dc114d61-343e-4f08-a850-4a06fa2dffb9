import { NextResponse } from 'next/server';
import { isTimeSlotAvailable } from '../../../lib/appointmentUtils.js';
import { timeSlots } from '../../../lib/utils.js';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');

    if (!date) {
      return NextResponse.json(
        { success: false, message: 'Data obbligatoria' },
        { status: 400 }
      );
    }

    // Check availability for each time slot
    const availability = timeSlots.map(time => ({
      time,
      available: isTimeSlotAvailable(date, time)
    }));

    return NextResponse.json({
      success: true,
      date,
      availability
    });

  } catch (error) {
    console.error('Error checking availability:', error);
    return NextResponse.json(
      { success: false, message: 'Errore del server' },
      { status: 500 }
    );
  }
}