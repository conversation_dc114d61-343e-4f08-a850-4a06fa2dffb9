import { NextResponse } from 'next/server';
import { isTimeSlotAvailable, getAppointmentsByDateRange } from '../../../lib/appointmentUtils.js';
import { timeSlots, isWeekday } from '../../../lib/utils.js';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');

    if (!date) {
      return NextResponse.json(
        { success: false, message: 'Data obbligatoria' },
        { status: 400 }
      );
    }

    // Validate that the date is a weekday
    if (!isWeekday(date)) {
      return NextResponse.json({
        success: false,
        message: 'Gli appuntamenti sono disponibili solo dal lunedì al venerdì',
        date,
        availability: [],
        availableSlots: [],
        bookedSlots: timeSlots
      });
    }

    // Check if the date is in the past
    const selectedDate = new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (selectedDate < today) {
      return NextResponse.json({
        success: false,
        message: 'Non è possibile prenotare appuntamenti per date passate',
        date,
        availability: [],
        availableSlots: [],
        bookedSlots: timeSlots
      });
    }

    // Get existing appointments for the date
    const existingAppointments = getAppointmentsByDateRange(date, date);
    const bookedTimes = existingAppointments
      .filter(app => app.status !== 'cancelled')
      .map(app => app.orario);

    // Check availability for each time slot
    const availability = timeSlots.map(time => {
      const available = isTimeSlotAvailable(date, time);
      const isBooked = bookedTimes.includes(time);

      return {
        time,
        available,
        isBooked,
        label: time,
        status: available ? 'available' : 'booked'
      };
    });

    // Separate available and booked slots
    const availableSlots = availability.filter(slot => slot.available).map(slot => slot.time);
    const bookedSlots = availability.filter(slot => !slot.available).map(slot => slot.time);

    // Group slots by time period
    const morningSlots = availability.filter(slot => {
      const hour = parseInt(slot.time.split(':')[0]);
      return hour >= 9 && hour <= 13;
    });

    const afternoonSlots = availability.filter(slot => {
      const hour = parseInt(slot.time.split(':')[0]);
      return hour >= 15 && hour <= 18;
    });

    return NextResponse.json({
      success: true,
      date,
      availability,
      availableSlots,
      bookedSlots,
      summary: {
        total: timeSlots.length,
        available: availableSlots.length,
        booked: bookedSlots.length,
        morningAvailable: morningSlots.filter(s => s.available).length,
        afternoonAvailable: afternoonSlots.filter(s => s.available).length
      },
      timeGroups: {
        morning: morningSlots,
        afternoon: afternoonSlots
      }
    });

  } catch (error) {
    console.error('Error checking availability:', error);
    return NextResponse.json(
      { success: false, message: 'Errore del server' },
      { status: 500 }
    );
  }
}