import { NextResponse } from 'next/server';
import { validateAdminCredentials, generateAdminToken } from '../../../../lib/authUtils.js';

export async function POST(request) {
  try {
    const { username, password } = await request.json();

    // Validate credentials
    if (!validateAdminCredentials(username, password)) {
      return NextResponse.json(
        { success: false, message: 'Credenziali non valide' },
        { status: 401 }
      );
    }

    // Generate JWT token
    const token = generateAdminToken(username);

    return NextResponse.json({
      success: true,
      message: 'Login effettuato con successo',
      token,
      user: {
        username,
        role: 'admin'
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { success: false, message: 'Errore del server' },
      { status: 500 }
    );
  }
}